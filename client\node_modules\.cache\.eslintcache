[{"C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\components\\ContactDetail.js": "4", "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\components\\Dashboard.js": "5", "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\components\\CSVImport.js": "6", "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\components\\ContactForm.js": "7", "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\components\\ConversationForm.js": "8", "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\services\\api.js": "9", "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\components\\ListForm.js": "10", "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\components\\ColumnMapper.js": "11"}, {"size": 535, "mtime": 1754939315456, "results": "12", "hashOfConfig": "13"}, {"size": 660, "mtime": 1754939560100, "results": "14", "hashOfConfig": "13"}, {"size": 362, "mtime": 1754939316145, "results": "15", "hashOfConfig": "13"}, {"size": 19393, "mtime": 1755027868583, "results": "16", "hashOfConfig": "13"}, {"size": 15339, "mtime": 1755025469528, "results": "17", "hashOfConfig": "13"}, {"size": 33605, "mtime": 1755026680958, "results": "18", "hashOfConfig": "13"}, {"size": 14320, "mtime": 1754942902125, "results": "19", "hashOfConfig": "13"}, {"size": 5031, "mtime": 1754939729152, "results": "20", "hashOfConfig": "13"}, {"size": 3820, "mtime": 1754942465843, "results": "21", "hashOfConfig": "13"}, {"size": 4913, "mtime": 1754941205456, "results": "22", "hashOfConfig": "13"}, {"size": 8458, "mtime": 1754942919479, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mct4", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\components\\ContactDetail.js", [], ["57"], "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\components\\Dashboard.js", [], ["58"], "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\components\\CSVImport.js", [], [], "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\components\\ContactForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\components\\ConversationForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\components\\ListForm.js", [], [], "C:\\Users\\<USER>\\Desktop\\email_dash\\client\\src\\components\\ColumnMapper.js", ["59", "60"], [], {"ruleId": "61", "severity": 1, "message": "62", "line": 19, "column": 6, "nodeType": "63", "endLine": 19, "endColumn": 10, "suggestions": "64", "suppressions": "65"}, {"ruleId": "61", "severity": 1, "message": "66", "line": 28, "column": 6, "nodeType": "63", "endLine": 28, "endColumn": 22, "suggestions": "67", "suppressions": "68"}, {"ruleId": "69", "severity": 1, "message": "70", "line": 50, "column": 9, "nodeType": "71", "messageId": "72", "endLine": 50, "endColumn": 31}, {"ruleId": "69", "severity": 1, "message": "73", "line": 51, "column": 71, "nodeType": "71", "messageId": "72", "endLine": 51, "endColumn": 83}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadContactData'. Either include it or remove the dependency array.", "ArrayExpression", ["74"], ["75"], "React Hook useEffect has a missing dependency: 'loadContacts'. Either include it or remove the dependency array.", ["76"], ["77"], "no-unused-vars", "'getFieldRecommendation' is assigned a value but never used.", "Identifier", "unusedVar", "'sampleValues' is assigned a value but never used.", {"desc": "78", "fix": "79"}, {"kind": "80", "justification": "81"}, {"desc": "82", "fix": "83"}, {"kind": "80", "justification": "81"}, "Update the dependencies array to be: [id, loadContactData]", {"range": "84", "text": "85"}, "directive", "", "Update the dependencies array to be: [loadContacts, selectedListId]", {"range": "86", "text": "87"}, [748, 752], "[id, loadContactData]", [946, 962], "[loadContacts, selectedListId]"]