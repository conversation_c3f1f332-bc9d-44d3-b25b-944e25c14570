import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { contactAPI, listAPI } from '../services/api';
import ContactForm from './ContactForm';
import CSVImport from './CSVImport';
import ListForm from './ListForm';

const Dashboard = () => {
  const [lists, setLists] = useState([]);
  const [contacts, setContacts] = useState([]);
  const [selectedListId, setSelectedListId] = useState('default');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showContactForm, setShowContactForm] = useState(false);
  const [showCSVImport, setShowCSVImport] = useState(false);
  const [showListForm, setShowListForm] = useState(false);
  const [editingList, setEditingList] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    if (selectedListId) {
      loadContacts();
    }
  }, [selectedListId]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadData = async () => {
    try {
      setLoading(true);
      const [listsResponse, contactsResponse] = await Promise.all([
        listAPI.getAll(),
        contactAPI.getAll()
      ]);
      setLists(listsResponse.data);
      setContacts(contactsResponse.data);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const loadContacts = async () => {
    try {
      const response = await contactAPI.getAll();
      const filteredContacts = response.data.filter(c => c.listId === selectedListId);
      setContacts(filteredContacts);
    } catch (err) {
      setError(err.message);
    }
  };

  const handleContactClick = (contactId) => {
    navigate(`/contact/${contactId}`);
  };

  const handleContactCreated = () => {
    setShowContactForm(false);
    loadData();
  };

  const handleCSVImported = () => {
    setShowCSVImport(false);
    loadData();
  };

  const handleListCreated = () => {
    setShowListForm(false);
    setEditingList(null);
    loadData();
  };

  const handleEditList = (list) => {
    setEditingList(list);
    setShowListForm(true);
  };

  const handleDeleteList = async (listId) => {
    if (listId === 'default') {
      alert('Cannot delete the default contact list');
      return;
    }

    const list = lists.find(l => l.id === listId);
    const contactCount = list?.contactCount || 0;

    let confirmMessage = `Are you sure you want to delete the list "${list?.name}"?`;
    if (contactCount > 0) {
      confirmMessage += `\n\nThis list contains ${contactCount} contact(s). They will be moved to the "General Contacts" list.`;
    }

    if (window.confirm(confirmMessage)) {
      try {
        const response = await listAPI.delete(listId);

        // Show success message with moved contacts info
        if (response.data.movedContacts > 0) {
          alert(`List deleted successfully. ${response.data.movedContacts} contact(s) were moved to "General Contacts".`);
        }

        // Switch to default list if we deleted the currently selected list
        if (selectedListId === listId) {
          setSelectedListId('default');
        }

        loadData();
      } catch (err) {
        setError(err.message);
        alert('Failed to delete list: ' + err.message);
      }
    }
  };

  const handleDeleteContact = async (contactId) => {
    if (window.confirm('Are you sure you want to delete this contact?')) {
      try {
        await contactAPI.delete(contactId);
        loadData();
      } catch (err) {
        setError(err.message);
      }
    }
  };

  const getCurrentList = () => {
    return lists.find(l => l.id === selectedListId) || { name: 'Unknown List', color: '#6c757d' };
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="dashboard">
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          Loading contacts...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="dashboard">
        <div style={{ textAlign: 'center', padding: '2rem', color: 'red' }}>
          Error: {error}
          <br />
          <button className="btn btn-primary" onClick={loadContacts} style={{ marginTop: '1rem' }}>
            Retry
          </button>
        </div>
      </div>
    );
  }

  const currentList = getCurrentList();

  return (
    <div className="dashboard">
      {/* List Navigation */}
      <div style={{
        marginBottom: '2rem',
        borderBottom: '1px solid #dee2e6',
        paddingBottom: '1rem'
      }}>
        <div
          className="list-navigation"
          style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '0.5rem',
            alignItems: 'center',
            overflowX: 'auto',
            paddingBottom: '0.5rem'
          }}>
        {lists.map((list) => (
          <div
            key={list.id}
            style={{
              position: 'relative',
              display: 'inline-block'
            }}
            className="list-tab"
          >
            <button
              onClick={() => setSelectedListId(list.id)}
              data-count={`(${list.contactCount || 0})`}
              style={{
                padding: '0.5rem 1rem',
                paddingRight: list.id !== 'default' ? '3rem' : '1rem',
                border: 'none',
                borderRadius: '6px',
                backgroundColor: selectedListId === list.id ? list.color : '#f8f9fa',
                color: selectedListId === list.id ? 'white' : '#495057',
                cursor: 'pointer',
                fontWeight: selectedListId === list.id ? 'bold' : 'normal',
                transition: 'all 0.2s',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                fontSize: '0.9rem',
                whiteSpace: 'nowrap',
                minWidth: 'fit-content',
                maxWidth: '200px'
              }}
            >
              <span
                style={{
                  width: '10px',
                  height: '10px',
                  borderRadius: '50%',
                  backgroundColor: selectedListId === list.id ? 'rgba(255,255,255,0.3)' : list.color,
                  flexShrink: 0
                }}
              />
              <span style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
                {list.name} ({list.contactCount || 0})
              </span>
            </button>

            {/* Action buttons for non-default lists */}
            {list.id !== 'default' && (
              <div
                className="list-actions"
                style={{
                  position: 'absolute',
                  top: '4px',
                  right: '4px',
                  display: 'flex',
                  gap: '2px'
                }}
              >
                {/* Edit button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEditList(list);
                  }}
                  style={{
                    width: '18px',
                    height: '18px',
                    border: 'none',
                    borderRadius: '50%',
                    backgroundColor: 'rgba(52, 152, 219, 0.8)',
                    color: 'white',
                    cursor: 'pointer',
                    fontSize: '9px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.2s'
                  }}
                  title={`Edit ${list.name} list`}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = '#3498db';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = 'rgba(52, 152, 219, 0.8)';
                  }}
                >
                  ✎
                </button>

                {/* Delete button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteList(list.id);
                  }}
                  style={{
                    width: '18px',
                    height: '18px',
                    border: 'none',
                    borderRadius: '50%',
                    backgroundColor: 'rgba(220, 53, 69, 0.8)',
                    color: 'white',
                    cursor: 'pointer',
                    fontSize: '11px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.2s'
                  }}
                  title={`Delete ${list.name} list`}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = '#dc3545';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = 'rgba(220, 53, 69, 0.8)';
                  }}
                >
                  ×
                </button>
              </div>
            )}
          </div>
        ))}
        <button
          onClick={() => setShowListForm(true)}
          style={{
            padding: '0.5rem 1rem',
            border: '2px dashed #dee2e6',
            borderRadius: '6px',
            backgroundColor: 'transparent',
            color: '#6c757d',
            cursor: 'pointer',
            transition: 'all 0.2s',
            fontSize: '0.9rem',
            whiteSpace: 'nowrap'
          }}
        >
          + Add List
        </button>
        </div>
      </div>

      <div className="dashboard-header">
        <h2 style={{ color: currentList.color }}>
          {currentList.name} ({contacts.length})
        </h2>
        <div>
          <button
            className="btn btn-success"
            onClick={() => setShowContactForm(true)}
            style={{ marginRight: '1rem' }}
          >
            Add Contact
          </button>
          <button
            className="btn btn-primary"
            onClick={() => setShowCSVImport(true)}
          >
            Import CSV
          </button>
        </div>
      </div>

      {contacts.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '3rem' }}>
          <h3>No contacts yet</h3>
          <p>Add your first contact or import from CSV to get started.</p>
        </div>
      ) : (
        <div className="contact-list">
          {contacts.map((contact) => (
            <div
              key={contact.id}
              className="contact-card"
              style={{ position: 'relative' }}
            >
              <div
                onClick={() => handleContactClick(contact.id)}
                style={{ cursor: 'pointer' }}
              >
                <div className="contact-name">
                  {contact.firstName && contact.lastName
                    ? `${contact.firstName} ${contact.lastName}`
                    : contact.name
                  }
                </div>
                <div className="contact-email">{contact.email}</div>

                {/* Company and title */}
                {(contact.company || contact.title) && (
                  <div style={{ color: '#6c757d', marginBottom: '0.5rem', fontSize: '0.9rem' }}>
                    {contact.title && contact.company
                      ? `${contact.title} at ${contact.company}`
                      : contact.title || contact.company
                    }
                  </div>
                )}

                {contact.phone && (
                  <div style={{ color: '#6c757d', marginBottom: '0.5rem' }}>
                    📞 {contact.phone}
                  </div>
                )}

                {/* Social media links */}
                {(contact.linkedinUrl || contact.website) && (
                  <div style={{
                    display: 'flex',
                    gap: '0.5rem',
                    marginBottom: '0.5rem',
                    fontSize: '0.8rem'
                  }}>
                    {contact.linkedinUrl && (
                      <a
                        href={contact.linkedinUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{ color: '#0077b5', textDecoration: 'none' }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        LinkedIn
                      </a>
                    )}
                    {contact.website && (
                      <a
                        href={contact.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{ color: '#3498db', textDecoration: 'none' }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        Website
                      </a>
                    )}
                  </div>
                )}

                <div className={`contact-status ${contact.status}`}>
                  {contact.status}
                </div>
                <div className="contact-meta">
                  <div>Conversations: {contact.conversationCount || 0}</div>
                  <div>Last contact: {formatDate(contact.lastContactDate)}</div>
                  <div>Added: {formatDate(contact.createdAt)}</div>
                </div>
              </div>

              {/* Delete button */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteContact(contact.id);
                }}
                style={{
                  position: 'absolute',
                  top: '0.5rem',
                  right: '0.5rem',
                  background: '#dc3545',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  width: '24px',
                  height: '24px',
                  fontSize: '12px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
                title="Delete contact"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}

      {showContactForm && (
        <ContactForm
          onClose={() => setShowContactForm(false)}
          onContactCreated={handleContactCreated}
          defaultListId={selectedListId}
          lists={lists}
        />
      )}

      {showCSVImport && (
        <CSVImport
          onClose={() => setShowCSVImport(false)}
          onImportComplete={handleCSVImported}
          defaultListId={selectedListId}
          lists={lists}
        />
      )}

      {showListForm && (
        <ListForm
          onClose={() => {
            setShowListForm(false);
            setEditingList(null);
          }}
          onListCreated={handleListCreated}
          list={editingList}
        />
      )}
    </div>
  );
};

export default Dashboard;
