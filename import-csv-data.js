const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');
const Contact = require('./server/models/Contact');
const { readJsonFile, writeJsonFile, CONTACTS_FILE, LISTS_FILE } = require('./server/utils/dataUtils');

// CSV file to list ID mapping
const CSV_TO_LIST_MAPPING = {
  'coffee_shops_300_mile_radius_pgh.csv': 'dd74d090-33f0-47cd-b859-5f2421921040',
  'pa_category_managers.csv': 'f14e7b5b-0f41-4afe-b74f-9133665f4aad',
  'pa_merchandiser_managers.csv': '2106b9fc-e987-4964-bd04-b7787503e566',
  'pa_purchasing_procurement_sales_managers.csv': 'f99bd3a1-5414-4906-bec0-a83dfdcd7b14',
  'pa_regional_managers.csv': '63cc4831-bdb9-4cee-aa38-ab1f72ef7c73',
  'super_local_coffee_ceo_contacts.csv': '25119c3a-ec96-471b-8514-7b7c48f5747b',
  'super_local_pgh_coffee_shops.csv': '476697e8-7fbe-436b-8e3b-4cdd2eb3e326',
  'super_local_restaurant_ceo_contacts.csv': '6041c5b2-3f15-4dd9-8cdb-36f2d6df0078'
};

// Column mapping detection (copied from import.js)
const detectColumnMapping = (headers) => {
  const mapping = {};
  
  headers.forEach(header => {
    const lowerHeader = header.toLowerCase().trim();
    
    // Name fields
    if (lowerHeader.includes('first') && lowerHeader.includes('name')) {
      mapping.firstName = header;
    } else if (lowerHeader.includes('last') && lowerHeader.includes('name')) {
      mapping.lastName = header;
    } else if (lowerHeader === 'name' || lowerHeader === 'full_name' || lowerHeader === 'fullname') {
      mapping.name = header;
    }
    
    // Contact fields
    else if (lowerHeader === 'email' || lowerHeader === 'email_address') {
      mapping.email = header;
    } else if (lowerHeader === 'phone' || lowerHeader === 'phone_number' || lowerHeader === 'telephone') {
      mapping.phone = header;
    }
    
    // Company fields
    else if (lowerHeader === 'company' || lowerHeader === 'organization' || lowerHeader === 'business') {
      mapping.company = header;
    } else if (lowerHeader === 'title' || lowerHeader === 'job_title' || lowerHeader === 'position') {
      mapping.title = header;
    } else if (lowerHeader === 'business_type' || lowerHeader === 'company_type') {
      mapping.companyType = header;
    } else if (lowerHeader === 'industry') {
      mapping.companyType = header; // Map industry to companyType for now
    }
    
    // Address fields
    else if (lowerHeader === 'address' || lowerHeader === 'street_address') {
      mapping.address = header;
    } else if (lowerHeader === 'city') {
      mapping.city = header;
    } else if (lowerHeader === 'state' || lowerHeader === 'province') {
      mapping.state = header;
    } else if (lowerHeader === 'zip' || lowerHeader === 'postal_code' || lowerHeader === 'zipcode') {
      mapping.zip = header;
    } else if (lowerHeader === 'country') {
      mapping.country = header;
    }
    
    // URL fields
    else if (lowerHeader === 'website' || lowerHeader === 'website_url' || lowerHeader === 'url') {
      mapping.website = header;
    } else if (lowerHeader.includes('linkedin')) {
      mapping.linkedinUrl = header;
    } else if (lowerHeader.includes('facebook')) {
      mapping.facebookUrl = header;
    } else if (lowerHeader.includes('instagram')) {
      mapping.instagramUrl = header;
    } else if (lowerHeader.includes('twitter')) {
      mapping.twitterUrl = header;
    }
    
    // Other fields
    else if (lowerHeader === 'notes' || lowerHeader === 'description') {
      mapping.notes = header;
    } else if (lowerHeader === 'status') {
      mapping.status = header;
    } else if (lowerHeader === 'headline' || lowerHeader === 'tagline') {
      mapping.headline = header;
    } else if (lowerHeader === 'seniority' || lowerHeader === 'level') {
      mapping.seniority = header;
    }
  });
  
  return mapping;
};

// Clean contact data (enhanced to capture all CSV data)
const cleanContactData = (rawData, mapping, headers) => {
  const cleaned = {};

  // Basic fields
  cleaned.firstName = mapping.firstName ? (rawData[mapping.firstName] || '').trim() : '';
  cleaned.lastName = mapping.lastName ? (rawData[mapping.lastName] || '').trim() : '';
  cleaned.name = mapping.name ? (rawData[mapping.name] || '').trim() : '';
  cleaned.email = mapping.email ? (rawData[mapping.email] || '').trim() : '';
  cleaned.phone = mapping.phone ? (rawData[mapping.phone] || '').trim() : '';
  cleaned.status = mapping.status ? (rawData[mapping.status] || '').trim().toLowerCase() : 'active';

  // Company fields
  cleaned.company = mapping.company ? (rawData[mapping.company] || '').trim() : '';
  cleaned.title = mapping.title ? (rawData[mapping.title] || '').trim() : '';
  cleaned.companyType = mapping.companyType ? (rawData[mapping.companyType] || '').trim() : '';

  // Address fields
  cleaned.address = mapping.address ? (rawData[mapping.address] || '').trim() : '';
  cleaned.city = mapping.city ? (rawData[mapping.city] || '').trim() : '';
  cleaned.state = mapping.state ? (rawData[mapping.state] || '').trim() : '';
  cleaned.zip = mapping.zip ? (rawData[mapping.zip] || '').trim() : '';
  cleaned.country = mapping.country ? (rawData[mapping.country] || '').trim() : '';

  // URL fields
  cleaned.website = mapping.website ? (rawData[mapping.website] || '').trim() : '';
  cleaned.linkedinUrl = mapping.linkedinUrl ? (rawData[mapping.linkedinUrl] || '').trim() : '';
  cleaned.facebookUrl = mapping.facebookUrl ? (rawData[mapping.facebookUrl] || '').trim() : '';
  cleaned.instagramUrl = mapping.instagramUrl ? (rawData[mapping.instagramUrl] || '').trim() : '';

  // Additional fields
  cleaned.notes = mapping.notes ? (rawData[mapping.notes] || '') : '';

  // Initialize customFields object for additional data
  cleaned.customFields = {};

  // Capture all additional CSV data that doesn't map to standard fields
  headers.forEach(header => {
    const value = (rawData[header] || '').toString().trim();
    if (value && !isStandardField(header)) {
      // Clean up field names for display
      const fieldName = header.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      cleaned.customFields[fieldName] = value;
    }
  });

  // Generate name from first/last name if name is empty
  if (!cleaned.name.trim() && (cleaned.firstName.trim() || cleaned.lastName.trim())) {
    cleaned.name = `${cleaned.firstName} ${cleaned.lastName}`.trim();
  }

  // Clean and validate email
  if (cleaned.email) {
    cleaned.email = cleaned.email.trim().toLowerCase();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(cleaned.email)) {
      cleaned.email = '';
    }
  }

  // Clean phone number
  if (cleaned.phone) {
    cleaned.phone = cleaned.phone.replace(/[^\d+\-\(\)\s\.]/g, '').trim();
  }

  return cleaned;
};

// Helper function to identify standard fields that shouldn't go into customFields
const isStandardField = (header) => {
  const standardFields = [
    'first_name', 'firstName', 'last_name', 'lastName', 'name', 'email', 'phone', 'status',
    'company', 'title', 'company_type', 'companyType', 'business_type', 'industry',
    'address', 'city', 'state', 'zip', 'country', 'zipcode', 'postal_code',
    'website', 'website_url', 'url', 'linkedin_url', 'linkedinUrl', 'facebook_url', 'facebookUrl',
    'instagram_url', 'instagramUrl', 'twitter_url', 'twitterUrl', 'notes', 'description'
  ];
  return standardFields.includes(header.toLowerCase()) || standardFields.includes(header);
};

// Update list contact count
const updateListContactCount = async (listId) => {
  try {
    const lists = await readJsonFile(LISTS_FILE);
    const contacts = await readJsonFile(CONTACTS_FILE);
    
    const listIndex = lists.findIndex(list => list.id === listId);
    if (listIndex !== -1) {
      const contactCount = contacts.filter(contact => contact.listId === listId).length;
      lists[listIndex].contactCount = contactCount;
      lists[listIndex].updatedAt = new Date().toISOString();
      
      await writeJsonFile(LISTS_FILE, lists);
      console.log(`Updated list ${lists[listIndex].name} contact count to ${contactCount}`);
    }
  } catch (error) {
    console.error('Error updating list contact count:', error);
  }
};

// Process a single CSV file
const processCsvFile = async (csvFilePath, listId) => {
  return new Promise((resolve, reject) => {
    const results = [];
    const errors = [];
    let lineNumber = 1;
    let columnMapping = null;
    let detectedHeaders = [];
    
    console.log(`Processing ${path.basename(csvFilePath)}...`);
    
    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('headers', (headers) => {
        detectedHeaders = headers;
        columnMapping = detectColumnMapping(headers);
        console.log(`  Detected ${headers.length} columns`);
      })
      .on('data', (data) => {
        lineNumber++;
        
        // Clean and map the data
        const contactData = cleanContactData(data, columnMapping, detectedHeaders);
        contactData.listId = listId;
        
        // Validate required fields
        const hasPersonName = contactData.name.trim() || contactData.firstName.trim() || contactData.lastName.trim();
        const hasCompanyName = contactData.company.trim();
        
        if (!hasPersonName && !hasCompanyName) {
          errors.push({
            line: lineNumber,
            error: 'Either a person name or company name is required',
            data: contactData
          });
          return;
        }
        
        // If no personal name but has company, use company as the name
        if (!hasPersonName && hasCompanyName) {
          contactData.name = contactData.company;
        }
        
        if (!contactData.email.trim()) {
          errors.push({
            line: lineNumber,
            error: 'Email is required but was empty or not found',
            data: contactData
          });
          return;
        }
        
        // Create new contact
        const newContact = new Contact(contactData);
        if (newContact.isValid()) {
          results.push(newContact.toJSON());
        } else {
          errors.push({
            line: lineNumber,
            error: 'Contact data validation failed',
            data: contactData
          });
        }
      })
      .on('end', () => {
        console.log(`  Processed ${lineNumber - 1} rows, ${results.length} valid contacts, ${errors.length} errors`);
        if (errors.length > 0) {
          console.log(`  First few errors:`, errors.slice(0, 3));
        }
        resolve({ results, errors });
      })
      .on('error', reject);
  });
};

// Main import function
const importAllCsvData = async () => {
  try {
    console.log('Starting CSV data import...');
    
    // Read existing contacts to check for duplicates
    const existingContacts = await readJsonFile(CONTACTS_FILE);
    const existingEmails = new Set(existingContacts.map(c => c.email.toLowerCase()));
    
    console.log(`Found ${existingContacts.length} existing contacts`);
    
    let totalNewContacts = 0;
    let totalSkipped = 0;
    
    // Process each CSV file
    for (const [csvFileName, listId] of Object.entries(CSV_TO_LIST_MAPPING)) {
      const csvFilePath = path.join(__dirname, 'data', csvFileName);
      
      if (!fs.existsSync(csvFilePath)) {
        console.log(`Skipping ${csvFileName} - file not found`);
        continue;
      }
      
      const { results, errors } = await processCsvFile(csvFilePath, listId);
      
      // Filter out duplicates
      const newContacts = results.filter(contact => {
        if (existingEmails.has(contact.email.toLowerCase())) {
          totalSkipped++;
          return false;
        }
        existingEmails.add(contact.email.toLowerCase());
        return true;
      });
      
      // Add new contacts to existing contacts
      existingContacts.push(...newContacts);
      totalNewContacts += newContacts.length;
      
      console.log(`  Added ${newContacts.length} new contacts (${results.length - newContacts.length} duplicates skipped)`);
    }
    
    // Save updated contacts
    if (totalNewContacts > 0) {
      await writeJsonFile(CONTACTS_FILE, existingContacts);
      console.log(`\nSaved ${totalNewContacts} new contacts to contacts.json`);
      
      // Update all list contact counts
      console.log('\nUpdating list contact counts...');
      for (const listId of Object.values(CSV_TO_LIST_MAPPING)) {
        await updateListContactCount(listId);
      }
    }
    
    console.log(`\nImport completed!`);
    console.log(`Total new contacts added: ${totalNewContacts}`);
    console.log(`Total duplicates skipped: ${totalSkipped}`);
    console.log(`Total contacts in system: ${existingContacts.length}`);
    
  } catch (error) {
    console.error('Error during import:', error);
  }
};

// Run the import
if (require.main === module) {
  importAllCsvData();
}

module.exports = { importAllCsvData };
