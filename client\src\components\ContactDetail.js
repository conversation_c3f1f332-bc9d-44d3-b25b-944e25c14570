import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { contactAPI, conversationAPI } from '../services/api';
import ConversationForm from './ConversationForm';
import ContactForm from './ContactForm';

const ContactDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [contact, setContact] = useState(null);
  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showConversationForm, setShowConversationForm] = useState(false);
  const [showContactForm, setShowContactForm] = useState(false);

  useEffect(() => {
    loadContactData();
  }, [id]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadContactData = async () => {
    try {
      setLoading(true);
      const [contactResponse, conversationsResponse] = await Promise.all([
        contactAPI.getById(id),
        conversationAPI.getByContactId(id)
      ]);
      
      setContact(contactResponse.data);
      setConversations(conversationsResponse.data);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleConversationCreated = () => {
    setShowConversationForm(false);
    loadContactData();
  };

  const handleContactUpdated = () => {
    setShowContactForm(false);
    loadContactData();
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString();
  };

  const groupConversationsByDate = (conversations) => {
    const groups = {};
    conversations.forEach(conv => {
      const date = formatDate(conv.timestamp);
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(conv);
    });
    return groups;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '2rem' }}>
        Loading contact details...
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ textAlign: 'center', padding: '2rem', color: 'red' }}>
        Error: {error}
        <br />
        <button className="btn btn-primary" onClick={loadContactData} style={{ marginTop: '1rem' }}>
          Retry
        </button>
        <button className="btn" onClick={() => navigate('/')} style={{ marginTop: '1rem', marginLeft: '1rem' }}>
          Back to Dashboard
        </button>
      </div>
    );
  }

  if (!contact) {
    return (
      <div style={{ textAlign: 'center', padding: '2rem' }}>
        Contact not found
        <br />
        <button className="btn btn-primary" onClick={() => navigate('/')} style={{ marginTop: '1rem' }}>
          Back to Dashboard
        </button>
      </div>
    );
  }

  const conversationGroups = groupConversationsByDate(conversations);

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto' }}>
      {/* Header */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '2rem',
        padding: '1rem',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
      }}>
        <div>
          <button 
            className="btn" 
            onClick={() => navigate('/')}
            style={{ marginRight: '1rem', backgroundColor: '#6c757d', color: 'white' }}
          >
            ← Back
          </button>
          <span style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>{contact.name}</span>
        </div>
        <div>
          <button 
            className="btn btn-primary" 
            onClick={() => setShowContactForm(true)}
            style={{ marginRight: '1rem' }}
          >
            Edit Contact
          </button>
          <button 
            className="btn btn-success" 
            onClick={() => setShowConversationForm(true)}
          >
            Add Conversation
          </button>
        </div>
      </div>

      {/* Contact Info */}
      <div style={{
        padding: '1.5rem',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        marginBottom: '2rem'
      }}>
        {/* Basic Information */}
        <div style={{ marginBottom: '1.5rem' }}>
          <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Contact Information</h4>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
            <div>
              <div style={{ marginBottom: '0.5rem' }}>
                <strong>Email:</strong>
                <a href={`mailto:${contact.email}`} style={{ marginLeft: '0.5rem', color: '#3498db' }}>
                  {contact.email}
                </a>
              </div>
              {contact.phone && (
                <div style={{ marginBottom: '0.5rem' }}>
                  <strong>Phone:</strong>
                  <a href={`tel:${contact.phone}`} style={{ marginLeft: '0.5rem', color: '#3498db' }}>
                    {contact.phone}
                  </a>
                </div>
              )}
            </div>
            <div>
              <div style={{ marginBottom: '0.5rem' }}>
                <strong>Status:</strong>
                <span className={`contact-status ${contact.status}`} style={{ marginLeft: '0.5rem' }}>
                  {contact.status}
                </span>
              </div>
              <div style={{ fontSize: '0.9rem', color: '#6c757d' }}>
                <div>Conversations: {contact.conversationCount || 0}</div>
                <div>Last contact: {contact.lastContactDate ? formatTimestamp(contact.lastContactDate) : 'Never'}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Company Information */}
        {(contact.company || contact.title || contact.companyType) && (
          <div style={{ marginBottom: '1.5rem' }}>
            <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Company Information</h4>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
              {contact.company && (
                <div style={{ marginBottom: '0.5rem' }}>
                  <strong>Company:</strong> {contact.company}
                </div>
              )}
              {contact.title && (
                <div style={{ marginBottom: '0.5rem' }}>
                  <strong>Title:</strong> {contact.title}
                </div>
              )}
              {contact.companyType && (
                <div style={{ marginBottom: '0.5rem' }}>
                  <strong>Company Type:</strong> {contact.companyType}
                </div>
              )}
            </div>
          </div>
        )}

        {/* URLs and Social Media */}
        {(contact.website || contact.linkedinUrl || contact.instagramUrl || contact.facebookUrl) && (
          <div style={{ marginBottom: '1.5rem' }}>
            <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Online Presence</h4>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '1rem' }}>
              {contact.website && (
                <a
                  href={contact.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#3498db',
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '4px',
                    fontSize: '0.9rem'
                  }}
                >
                  🌐 Website
                </a>
              )}
              {contact.linkedinUrl && (
                <a
                  href={contact.linkedinUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#0077b5',
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '4px',
                    fontSize: '0.9rem'
                  }}
                >
                  💼 LinkedIn
                </a>
              )}
              {contact.instagramUrl && (
                <a
                  href={contact.instagramUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#e4405f',
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '4px',
                    fontSize: '0.9rem'
                  }}
                >
                  📷 Instagram
                </a>
              )}
              {contact.facebookUrl && (
                <a
                  href={contact.facebookUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#1877f2',
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '4px',
                    fontSize: '0.9rem'
                  }}
                >
                  📘 Facebook
                </a>
              )}
            </div>
          </div>
        )}

        {/* Address Information */}
        {(contact.address || contact.city || contact.state || contact.zip || contact.country) && (
          <div style={{ marginBottom: '1.5rem' }}>
            <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Address</h4>
            <div>
              {contact.address && <div>{contact.address}</div>}
              <div>
                {[contact.city, contact.state, contact.zip].filter(Boolean).join(', ')}
                {contact.country && <div>{contact.country}</div>}
              </div>
            </div>
          </div>
        )}

        {/* Custom Fields */}
        {contact.customFields && Object.keys(contact.customFields).length > 0 && (
          <div style={{ marginBottom: '1.5rem' }}>
            <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Additional Information</h4>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '1rem'
            }}>
              {Object.entries(contact.customFields).map(([fieldName, value]) => (
                <div key={fieldName} style={{
                  padding: '0.75rem',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '4px',
                  border: '1px solid #e9ecef'
                }}>
                  <div style={{
                    fontWeight: 'bold',
                    fontSize: '0.9rem',
                    marginBottom: '0.25rem',
                    color: '#495057'
                  }}>
                    {fieldName}
                  </div>
                  <div style={{ fontSize: '0.9rem', lineHeight: '1.4' }}>
                    {value}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Notes */}
        {contact.notes && (
          <div>
            <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Notes</h4>
            <div style={{
              padding: '1rem',
              backgroundColor: '#f8f9fa',
              borderRadius: '4px',
              fontSize: '0.9rem',
              lineHeight: '1.5'
            }}>
              {contact.notes}
            </div>
          </div>
        )}
      </div>

      {/* Conversations */}
      <div style={{ 
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        minHeight: '400px'
      }}>
        <div style={{ 
          padding: '1rem',
          borderBottom: '1px solid #eee',
          fontWeight: 'bold'
        }}>
          Conversations ({conversations.length})
        </div>
        
        <div style={{ padding: '1rem', maxHeight: '500px', overflowY: 'auto' }}>
          {conversations.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '2rem', color: '#6c757d' }}>
              No conversations yet. Add the first conversation to get started.
            </div>
          ) : (
            Object.entries(conversationGroups).map(([date, dayConversations]) => (
              <div key={date} style={{ marginBottom: '2rem' }}>
                <div style={{ 
                  textAlign: 'center',
                  margin: '1rem 0',
                  padding: '0.5rem',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '4px',
                  fontSize: '0.9rem',
                  color: '#6c757d'
                }}>
                  {date}
                </div>
                
                {dayConversations.map((conversation) => (
                  <div 
                    key={conversation.id}
                    style={{
                      display: 'flex',
                      justifyContent: conversation.direction === 'sent' ? 'flex-end' : 'flex-start',
                      marginBottom: '1rem'
                    }}
                  >
                    <div style={{
                      maxWidth: '70%',
                      padding: '1rem',
                      borderRadius: '12px',
                      backgroundColor: conversation.direction === 'sent' ? '#3498db' : '#e9ecef',
                      color: conversation.direction === 'sent' ? 'white' : '#2c3e50'
                    }}>
                      {conversation.subject && (
                        <div style={{ 
                          fontWeight: 'bold', 
                          marginBottom: '0.5rem',
                          fontSize: '0.9rem'
                        }}>
                          {conversation.subject}
                        </div>
                      )}
                      <div style={{ marginBottom: '0.5rem' }}>
                        {conversation.content}
                      </div>
                      <div style={{ 
                        fontSize: '0.8rem',
                        opacity: 0.8,
                        textAlign: 'right'
                      }}>
                        {new Date(conversation.timestamp).toLocaleTimeString()}
                        {conversation.status !== 'read' && (
                          <span style={{ marginLeft: '0.5rem' }}>
                            • {conversation.status}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ))
          )}
        </div>
      </div>

      {showConversationForm && (
        <ConversationForm 
          contactId={id}
          onClose={() => setShowConversationForm(false)}
          onConversationCreated={handleConversationCreated}
        />
      )}

      {showContactForm && (
        <ContactForm 
          contact={contact}
          onClose={() => setShowContactForm(false)}
          onContactCreated={handleContactUpdated}
        />
      )}
    </div>
  );
};

export default ContactDetail;
