const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');
const { readJsonFile, writeJsonFile, CONTACTS_FILE } = require('./server/utils/dataUtils');

// CSV file to list ID mapping
const CSV_TO_LIST_MAPPING = {
  'coffee_shops_300_mile_radius_pgh.csv': 'dd74d090-33f0-47cd-b859-5f2421921040',
  'pa_category_managers.csv': 'f14e7b5b-0f41-4afe-b74f-9133665f4aad',
  'pa_merchandiser_managers.csv': '2106b9fc-e987-4964-bd04-b7787503e566',
  'pa_purchasing_procurement_sales_managers.csv': 'f99bd3a1-5414-4906-bec0-a83dfdcd7b14',
  'pa_regional_managers.csv': '63cc4831-bdb9-4cee-aa38-ab1f72ef7c73',
  'super_local_coffee_ceo_contacts.csv': '25119c3a-ec96-471b-8514-7b7c48f5747b',
  'super_local_pgh_coffee_shops.csv': '476697e8-7fbe-436b-8e3b-4cdd2eb3e326',
  'super_local_restaurant_ceo_contacts.csv': '6041c5b2-3f15-4dd9-8cdb-36f2d6df0078'
};

// Helper function to identify standard fields that shouldn't go into customFields
const isStandardField = (header) => {
  const standardFields = [
    'first_name', 'firstName', 'last_name', 'lastName', 'name', 'email', 'phone', 'status',
    'company', 'title', 'company_type', 'companyType', 'business_type', 'industry',
    'address', 'city', 'state', 'zip', 'country', 'zipcode', 'postal_code',
    'website', 'website_url', 'url', 'linkedin_url', 'linkedinUrl', 'facebook_url', 'facebookUrl',
    'instagram_url', 'instagramUrl', 'twitter_url', 'twitterUrl', 'notes', 'description'
  ];
  return standardFields.includes(header.toLowerCase()) || standardFields.includes(header);
};

// Process a single CSV file and return email-to-data mapping
const processCsvFile = async (csvFilePath, listId) => {
  return new Promise((resolve, reject) => {
    const emailToDataMap = new Map();
    let lineNumber = 1;
    let headers = [];
    
    console.log(`Processing ${path.basename(csvFilePath)}...`);
    
    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('headers', (csvHeaders) => {
        headers = csvHeaders;
        console.log(`  Detected ${headers.length} columns`);
      })
      .on('data', (data) => {
        lineNumber++;
        
        const email = (data.email || '').trim().toLowerCase();
        if (!email) return;
        
        // Collect all additional data
        const customFields = {};
        headers.forEach(header => {
          const value = (data[header] || '').toString().trim();
          if (value && !isStandardField(header)) {
            // Clean up field names for display
            const fieldName = header.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            customFields[fieldName] = value;
          }
        });
        
        emailToDataMap.set(email, { customFields, listId });
      })
      .on('end', () => {
        console.log(`  Processed ${lineNumber - 1} rows, found ${emailToDataMap.size} unique emails`);
        resolve(emailToDataMap);
      })
      .on('error', reject);
  });
};

// Main update function
const updateContactsWithAdditionalData = async () => {
  try {
    console.log('Starting contact data update...');
    
    // Read existing contacts
    const contacts = await readJsonFile(CONTACTS_FILE);
    console.log(`Found ${contacts.length} existing contacts`);
    
    // Process all CSV files and collect additional data
    const allAdditionalData = new Map();
    
    for (const [csvFileName, listId] of Object.entries(CSV_TO_LIST_MAPPING)) {
      const csvFilePath = path.join(__dirname, 'data', csvFileName);
      
      if (!fs.existsSync(csvFilePath)) {
        console.log(`Skipping ${csvFileName} - file not found`);
        continue;
      }
      
      const emailToDataMap = await processCsvFile(csvFilePath, listId);
      
      // Merge with existing data
      for (const [email, data] of emailToDataMap) {
        allAdditionalData.set(email, data);
      }
    }
    
    console.log(`\nCollected additional data for ${allAdditionalData.size} unique emails`);
    
    // Update contacts with additional data
    let updatedCount = 0;
    contacts.forEach(contact => {
      const email = contact.email.toLowerCase();
      const additionalData = allAdditionalData.get(email);
      
      if (additionalData) {
        // Merge customFields, preserving existing ones
        contact.customFields = contact.customFields || {};
        Object.assign(contact.customFields, additionalData.customFields);
        contact.updatedAt = new Date().toISOString();
        updatedCount++;
      }
    });
    
    // Save updated contacts
    if (updatedCount > 0) {
      await writeJsonFile(CONTACTS_FILE, contacts);
      console.log(`\nUpdated ${updatedCount} contacts with additional data`);
    } else {
      console.log('\nNo contacts were updated');
    }
    
    // Show sample of additional data fields
    console.log('\nSample of additional data fields captured:');
    const sampleContact = contacts.find(c => c.customFields && Object.keys(c.customFields).length > 0);
    if (sampleContact) {
      console.log(`Contact: ${sampleContact.name}`);
      console.log('Additional fields:', Object.keys(sampleContact.customFields).slice(0, 10));
    }
    
    console.log('\nUpdate completed!');
    
  } catch (error) {
    console.error('Error during update:', error);
  }
};

// Run the update
if (require.main === module) {
  updateContactsWithAdditionalData();
}

module.exports = { updateContactsWithAdditionalData };
